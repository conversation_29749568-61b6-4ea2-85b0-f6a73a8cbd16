using Ledger.Db.Models;
using Ledger.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Ledger.SimpleTest;

public class TestCategories
{
    public static async Task Main(string[] args)
    {
        Console.WriteLine("=== 测试分类数据 ===");
        
        try
        {
            // 配置数据库连接
            var connectionString = "Host=aws-0-ap-southeast-1.pooler.supabase.com;Port=6543;Database=postgres;Username=postgres.fexuauldombukkgqxbre;Password=****************;SSL Mode=Require;Trust Server Certificate=true;Timeout=30;Command Timeout=30;Connection Idle Lifetime=300;Maximum Pool Size=20";
            
            var options = new DbContextOptionsBuilder<LedgerContext>()
                .UseNpgsql(connectionString)
                .Options;
            
            using var context = new LedgerContext(options);
            
            // 创建简单的日志记录器
            using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            var logger = loggerFactory.CreateLogger<CategoryService>();
            
            // 创建CategoryService
            var categoryService = new CategoryService(context, logger);
            
            Console.WriteLine("\n--- 测试直接查询分类表 ---");
            
            // 直接查询所有分类
            var allCategories = await context.Categories.ToListAsync();
            Console.WriteLine($"数据库中总共有 {allCategories.Count} 个分类");
            
            foreach (var category in allCategories)
            {
                Console.WriteLine($"  ID: {category.Id}, 名称: {category.Name}, 类型: {category.Type}, 图标: {category.Icon}");
            }
            
            Console.WriteLine("\n--- 测试支出分类查询 ---");
            
            try
            {
                var expenseCategories = await categoryService.GetCategoriesByTypeAsync("expense");
                Console.WriteLine($"✅ 支出分类查询成功！找到 {expenseCategories.Count} 个分类");
                
                foreach (var category in expenseCategories)
                {
                    Console.WriteLine($"  {category.Icon} {category.Name}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 支出分类查询失败: {ex.Message}");
            }
            
            Console.WriteLine("\n--- 测试收入分类查询 ---");
            
            try
            {
                var incomeCategories = await categoryService.GetCategoriesByTypeAsync("income");
                Console.WriteLine($"✅ 收入分类查询成功！找到 {incomeCategories.Count} 个分类");
                
                foreach (var category in incomeCategories)
                {
                    Console.WriteLine($"  {category.Icon} {category.Name}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 收入分类查询失败: {ex.Message}");
            }
            
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 测试失败: {ex.Message}");
            Console.WriteLine($"详细错误: {ex}");
        }
        
        Console.WriteLine("\n测试完成，按任意键退出...");
        Console.ReadKey();
    }
}
