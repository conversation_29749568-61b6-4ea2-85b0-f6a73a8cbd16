using Ledger.Db.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Ledger.SimpleTest;

public class DataQuery
{
    public static async Task QueryExistingData()
    {
        Console.WriteLine("=== 查询现有数据 ===");
        
        try
        {
            // 读取配置
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json")
                .Build();
            
            var connectionString = configuration.GetConnectionString("DefaultConnection");
            
            // 创建数据库上下文
            var options = new DbContextOptionsBuilder<LedgerContext>()
                .UseNpgsql(connectionString)
                .Options;
            
            using var context = new LedgerContext(options);
            
            // 查询用户数据
            Console.WriteLine("\n--- 用户数据 ---");
            try
            {
                using var cts1 = new CancellationTokenSource(TimeSpan.FromSeconds(10));
                var users = await context.Users.ToListAsync(cts1.Token);
                Console.WriteLine($"用户数量: {users.Count}");
                foreach (var user in users)
                {
                    Console.WriteLine($"  ID: {user.Id}, 用户名: {user.Username}, 昵称: {user.Nickname}");
                }
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine("⚠️ 查询用户数据超时");
            }
            
            // 查询分类数据
            Console.WriteLine("\n--- 分类数据 ---");
            try
            {
                using var cts2 = new CancellationTokenSource(TimeSpan.FromSeconds(10));
                var categories = await context.Categories.ToListAsync(cts2.Token);
                Console.WriteLine($"分类数量: {categories.Count}");
                foreach (var category in categories.Take(10))
                {
                    Console.WriteLine($"  ID: {category.Id}, 名称: {category.Name}, 类型: {category.Type}, 图标: {category.Icon}");
                }
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine("⚠️ 查询分类数据超时");
            }
            
            // 查询交易数据
            Console.WriteLine("\n--- 交易数据 ---");
            try
            {
                using var cts3 = new CancellationTokenSource(TimeSpan.FromSeconds(10));
                var transactions = await context.Transactions
                    .Include(t => t.Category)
                    .Include(t => t.User)
                    .OrderByDescending(t => t.TransactionDate)
                    .Take(20)
                    .ToListAsync(cts3.Token);
                
                Console.WriteLine($"交易数量: {transactions.Count}");
                foreach (var transaction in transactions)
                {
                    Console.WriteLine($"  ID: {transaction.Id}, 日期: {transaction.TransactionDate}, " +
                                    $"金额: {transaction.Amount}, 类型: {transaction.Type}, " +
                                    $"描述: {transaction.Description}, 分类: {transaction.Category?.Name}, " +
                                    $"用户: {transaction.User?.Username}, 年月: {transaction.YearMonth}");
                }
                
                // 按年月统计
                Console.WriteLine("\n--- 按年月统计 ---");
                var monthlyStats = transactions
                    .GroupBy(t => t.YearMonth)
                    .Select(g => new { YearMonth = g.Key, Count = g.Count() })
                    .OrderByDescending(x => x.YearMonth)
                    .ToList();
                
                foreach (var stat in monthlyStats)
                {
                    Console.WriteLine($"  {stat.YearMonth}: {stat.Count} 笔交易");
                }
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine("⚠️ 查询交易数据超时");
            }
            
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 查询失败: {ex.Message}");
            Console.WriteLine($"详细错误: {ex}");
        }
        
        Console.WriteLine("\n查询完成，按任意键退出...");
        Console.ReadKey();
    }
}
