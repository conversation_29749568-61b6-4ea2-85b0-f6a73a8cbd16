using Ledger.Services;
using Ledger.Db.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Ledger.Test;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("=== 个人记账本测试程序 ===");
        Console.WriteLine("选择测试类型:");
        Console.WriteLine("1. LocalDB 迁移测试");
        Console.WriteLine("2. 原有功能测试");
        Console.Write("请输入选择 (1 或 2): ");

        var choice = Console.ReadLine();

        if (choice == "1")
        {
            await LocalDbMigrationTest.RunTestAsync();
        }
        else
        {
            await RunOriginalTestsAsync();
        }
    }

    static async Task RunOriginalTestsAsync()
    {
        Console.WriteLine("=== 运行原有功能测试 ===");

        // 创建主机
        var host = CreateHostBuilder().Build();
        await host.StartAsync();

        try
        {
            // 初始化数据
            var dataInitService = host.Services.GetRequiredService<DataInitializationService>();
            await dataInitService.InitializeDefaultDataAsync();
            Console.WriteLine("✓ 数据初始化完成");

            // 测试分类服务
            await TestCategoryService(host.Services);

            // 测试交易服务
            await TestTransactionService(host.Services);

            Console.WriteLine("\n=== 测试完成 ===");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 测试失败: {ex.Message}");
            Console.WriteLine($"详细错误: {ex}");
        }
        finally
        {
            await host.StopAsync();
        }
    }

    static async Task TestCategoryService(IServiceProvider services)
    {
        Console.WriteLine("\n--- 测试分类服务 ---");

        var categoryService = services.GetRequiredService<ICategoryService>();

        // 获取所有分类
        var categories = await categoryService.GetAllCategoriesAsync();
        Console.WriteLine($"✓ 总分类数: {categories.Count}");

        // 获取支出分类
        var expenseCategories = await categoryService.GetCategoriesByTypeAsync("expense");
        Console.WriteLine($"✓ 支出分类数: {expenseCategories.Count}");

        // 获取收入分类
        var incomeCategories = await categoryService.GetCategoriesByTypeAsync("income");
        Console.WriteLine($"✓ 收入分类数: {incomeCategories.Count}");

        // 显示前几个分类
        Console.WriteLine("前5个支出分类:");
        foreach (var category in expenseCategories.Take(5))
        {
            Console.WriteLine($"  {category.Icon} {category.Name}");
        }
    }

    static async Task TestTransactionService(IServiceProvider services)
    {
        Console.WriteLine("\n--- 测试交易服务 ---");

        var transactionService = services.GetRequiredService<ITransactionService>();
        var categoryService = services.GetRequiredService<ICategoryService>();

        // 获取一个分类用于测试
        var categories = await categoryService.GetCategoriesByTypeAsync("expense");
        if (categories.Count == 0)
        {
            Console.WriteLine("❌ 没有找到支出分类");
            return;
        }

        var testCategory = categories.First();

        // 创建测试交易
        var transaction = new Transaction
        {
            Amount = 50.00m,
            Type = "expense",
            Description = "测试支出记录",
            TransactionDate = DateOnly.FromDateTime(DateTime.Today),
            CategoryId = testCategory.Id,
            UserId = 1,
            IsDeleted = false
        };

        // 添加交易
        var addedTransaction = await transactionService.AddTransactionAsync(transaction);
        Console.WriteLine($"✓ 添加交易成功，ID: {addedTransaction.Id}");

        // 获取当月交易
        var currentMonth = DateTime.Now.ToString("yyyy-MM");
        var transactions = await transactionService.GetMonthlyTransactionsAsync(1, currentMonth);
        Console.WriteLine($"✓ 当月交易数: {transactions.Count}");

        // 获取月度统计
        var statistics = await transactionService.GetMonthlyStatisticsAsync(1, currentMonth);
        Console.WriteLine($"✓ 月度统计:");
        Console.WriteLine($"  总收入: ¥{statistics.TotalIncome:F2}");
        Console.WriteLine($"  总支出: ¥{statistics.TotalExpense:F2}");
        Console.WriteLine($"  净收入: ¥{statistics.NetIncome:F2}");
        Console.WriteLine($"  交易笔数: {statistics.TransactionCount}");

        // 显示最近的交易
        Console.WriteLine("最近的交易:");
        foreach (var t in transactions.Take(3))
        {
            Console.WriteLine($"  {t.TransactionDate:MM-dd} {t.Category?.Icon} {t.Category?.Name} {(t.Type == "income" ? "+" : "-")}¥{t.Amount:F2} {t.Description}");
        }
    }

    static IHostBuilder CreateHostBuilder()
    {
        return Host.CreateDefaultBuilder()
            .ConfigureAppConfiguration((context, config) =>
            {
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
            })
            .ConfigureServices((context, services) =>
            {
                // 配置数据库上下文
                var connectionString = context.Configuration.GetConnectionString("DefaultConnection");
                services.AddDbContext<LedgerContext>(options =>
                    options.UseSqlServer(connectionString));

                // 注册服务
                services.AddScoped<ITransactionService, TransactionService>();
                services.AddScoped<ICategoryService, CategoryService>();
                services.AddScoped<DataInitializationService>();
                services.AddScoped<LocalDbDataInitializationService>();

                // 配置日志
                services.AddLogging(builder =>
                {
                    builder.AddConsole();
                    builder.SetMinimumLevel(LogLevel.Information);
                });
            });
    }
}
