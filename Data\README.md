# 数据库文件目录

这个目录包含了个人记账本应用的 LocalDB 数据库文件。

## 📁 文件说明

- **LedgerDb.mdf** - 主数据文件，包含所有的表结构和数据
- **LedgerDb_log.ldf** - 事务日志文件，记录数据库的所有更改

## 🔧 数据库配置

**连接字符串：**
```
Server=(localdb)\MSSQLLocalDB;Database=LedgerDb;AttachDbFilename=F:\dotnet_workspace\ledger\Data\LedgerDb.mdf;Trusted_Connection=true;MultipleActiveResultSets=true
```

## 📋 数据库表结构

- **User** - 用户信息表
- **Categories** - 收支分类表
- **Transactions** - 交易记录表

## 💾 备份建议

1. **定期备份**：建议定期复制这两个文件到安全位置
2. **版本控制**：这些文件已被 `.gitignore` 排除，不会提交到 Git
3. **迁移**：如需迁移到其他机器，只需复制这两个文件并更新连接字符串中的路径

## 🚀 优势

- ✅ **自包含**：数据库文件与项目代码在同一目录
- ✅ **便携性**：可以轻松复制整个项目
- ✅ **版本控制友好**：数据文件不会污染代码仓库
- ✅ **开发便利**：无需额外的数据库服务器配置

## ⚠️ 注意事项

- 请勿手动编辑这些文件
- 确保 LocalDB 实例正在运行
- 如果移动项目位置，需要更新连接字符串中的文件路径
