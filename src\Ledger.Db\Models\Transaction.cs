﻿using System;
using System.Collections.Generic;

namespace Ledger.Db.Models;

/// <summary>
/// 收支流水记录表
/// </summary>
public partial class Transaction
{
    public int Id { get; set; }

    /// <summary>
    /// 金额
    /// </summary>
    public decimal Amount { get; set; }

    /// <summary>
    /// 类型:income(收入)/expense(支出)
    /// </summary>
    public string Type { get; set; } = null!;

    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 交易日期
    /// </summary>
    public DateOnly TransactionDate { get; set; }

    public DateTime CreatedAt { get; set; }

    public DateTime UpdatedAt { get; set; }

    public int CategoryId { get; set; }

    public int UserId { get; set; }

    /// <summary>
    /// 软删除标记
    /// </summary>
    public bool IsDeleted { get; set; }

    /// <summary>
    /// 年月(YYYY-MM)
    /// </summary>
    public string YearMonth { get; set; } = null!;

    public virtual Category Category { get; set; } = null!;

    public virtual User User { get; set; } = null!;
}
