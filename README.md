# 个人记账本 (Personal Ledger)

一个基于 .NET 8 和 WPF 的个人收支流水记录应用程序，支持月度统计和数据分析。

## 项目概述

本项目实现了一个完整的个人记账系统，包括：

- 📊 **收支流水记录** - 记录日常收入和支出
- 📈 **月度统计分析** - 自动计算月度收支情况
- 🏷️ **分类管理** - 支持收入和支出分类
- 💾 **数据持久化** - 使用 PostgreSQL 数据库存储
- 🎨 **现代化界面** - 基于 WPF 的美观用户界面

## 技术栈

- **前端**: .NET 8 WPF (Windows Presentation Foundation)
- **后端**: .NET 8 C#
- **数据库**: PostgreSQL (Supabase)
- **ORM**: Entity Framework Core 9.0
- **架构模式**: MVVM (Model-View-ViewModel)
- **依赖注入**: Microsoft.Extensions.DependencyInjection

## 项目结构

```
src/
├── Ledger.Db/              # 数据访问层
│   ├── Models/              # 数据模型
│   │   ├── User.cs         # 用户模型
│   │   ├── Category.cs     # 分类模型
│   │   ├── Transaction.cs  # 交易模型
│   │   └── LedgerContext.cs # EF 数据库上下文
│   └── Ledger.Db.csproj
├── Ledger.Services/         # 业务逻辑层
│   ├── ITransactionService.cs    # 交易服务接口
│   ├── TransactionService.cs     # 交易服务实现
│   ├── ICategoryService.cs       # 分类服务接口
│   ├── CategoryService.cs        # 分类服务实现
│   ├── DataInitializationService.cs # 数据初始化服务
│   └── Ledger.Services.csproj
├── Ledger.App/              # WPF 应用程序
│   ├── ViewModels/          # 视图模型
│   │   ├── MainViewModel.cs
│   │   ├── AddTransactionViewModel.cs
│   │   ├── MonthlyStatisticsViewModel.cs
│   │   ├── TransactionItemViewModel.cs
│   │   └── ViewModelBase.cs
│   ├── Commands/            # 命令实现
│   │   ├── RelayCommand.cs
│   │   └── AsyncRelayCommand.cs
│   ├── Converters/          # 数据转换器
│   │   └── Converters.cs
│   ├── MainWindow.xaml      # 主窗口界面
│   ├── App.xaml.cs          # 应用程序入口
│   └── Ledger.App.csproj
├── Ledger.Test/             # 集成测试
└── Ledger.SimpleTest/       # 简单测试
```

## 核心功能

### 1. 交易流水管理
- ✅ 添加收入/支出记录
- ✅ 按月份查看流水列表
- ✅ 支持分类和描述
- ✅ 按时间降序排列

### 2. 分类管理
- ✅ 预设收入/支出分类
- ✅ 支持图标和排序
- ✅ 分类统计功能

### 3. 月度统计
- ✅ 总收入/总支出计算
- ✅ 净收入计算
- ✅ 交易笔数统计
- ✅ 平均金额计算

### 4. 用户界面
- ✅ 现代化 WPF 界面
- ✅ 响应式布局
- ✅ 数据绑定
- ✅ 命令模式

## 数据模型

### User (用户)
- 用户基本信息
- 支持多种登录方式
- 关联交易记录

### Category (分类)
- 收入/支出分类
- 图标和排序支持
- 默认分类标记

### Transaction (交易)
- 金额、类型、日期
- 关联用户和分类
- 软删除支持
- 年月字段便于查询

## 快速开始

### 环境要求
- .NET 8 SDK
- Visual Studio 2022 或 VS Code
- PostgreSQL 数据库 (或 Supabase 账户)

### 运行步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd ledger
   ```

2. **配置数据库**
   - 更新 `src/Ledger.App/appsettings.json` 中的连接字符串
   - 确保数据库可访问

3. **构建项目**
   ```bash
   dotnet build src/PersonalLedger.sln
   ```

4. **运行应用**
   ```bash
   dotnet run --project src/Ledger.App
   ```

5. **运行测试**
   ```bash
   # 简单模型测试
   dotnet run --project src/Ledger.SimpleTest
   
   # 完整功能测试 (需要数据库连接)
   dotnet run --project src/Ledger.Test
   ```

## 默认数据

应用程序首次运行时会自动创建默认分类：

**支出分类**: 餐饮🍽️、交通🚗、购物🛒、娱乐🎮、医疗🏥、教育📚、住房🏠、通讯📱、服装👕、其他💸

**收入分类**: 工资💰、奖金🎁、投资收益📈、兼职收入💼、红包🧧、退款↩️、其他💵

## 主要特性

- 🎯 **MVVM 架构** - 清晰的代码分离
- 🔄 **依赖注入** - 松耦合设计
- 📱 **响应式界面** - 现代化用户体验
- 🛡️ **数据验证** - 输入验证和错误处理
- 🔍 **实时搜索** - 快速查找交易记录
- 📊 **统计分析** - 详细的财务分析
- 💾 **数据持久化** - 可靠的数据存储

## 开发说明

### 架构设计
- **分层架构**: 数据层、服务层、表示层
- **MVVM 模式**: 视图与业务逻辑分离
- **依赖注入**: 提高可测试性和可维护性

### 代码规范
- 使用 C# 命名约定
- 遵循 SOLID 原则
- 完整的 XML 文档注释
- 异步编程模式

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 贡献

欢迎提交 Issue 和 Pull Request！

## 更新日志

### v1.0.0 (2025-05-26)
- ✅ 初始版本发布
- ✅ 基本的收支记录功能
- ✅ 月度统计分析
- ✅ WPF 用户界面
- ✅ PostgreSQL 数据库支持
