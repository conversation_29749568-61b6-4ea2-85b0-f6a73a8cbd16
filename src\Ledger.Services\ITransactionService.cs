using Ledger.Db.Models;

namespace Ledger.Services;

/// <summary>
/// 交易流水服务接口
/// </summary>
public interface ITransactionService
{
    /// <summary>
    /// 获取指定用户指定月份的交易流水列表（按时间降序）
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="yearMonth">年月(YYYY-MM)</param>
    /// <returns>交易流水列表</returns>
    Task<List<Transaction>> GetMonthlyTransactionsAsync(int userId, string yearMonth);

    /// <summary>
    /// 添加新的交易流水
    /// </summary>
    /// <param name="transaction">交易流水对象</param>
    /// <returns>添加后的交易流水对象</returns>
    Task<Transaction> AddTransactionAsync(Transaction transaction);

    /// <summary>
    /// 更新交易流水
    /// </summary>
    /// <param name="transaction">交易流水对象</param>
    /// <returns>更新后的交易流水对象</returns>
    Task<Transaction> UpdateTransactionAsync(Transaction transaction);

    /// <summary>
    /// 删除交易流水（软删除）
    /// </summary>
    /// <param name="transactionId">交易流水ID</param>
    /// <returns>是否删除成功</returns>
    Task<bool> DeleteTransactionAsync(int transactionId);

    /// <summary>
    /// 获取指定用户指定月份的收支统计
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="yearMonth">年月(YYYY-MM)</param>
    /// <returns>收支统计信息</returns>
    Task<MonthlyStatistics> GetMonthlyStatisticsAsync(int userId, string yearMonth);

    /// <summary>
    /// 获取指定用户指定年份的月度收支统计列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="year">年份</param>
    /// <returns>月度收支统计列表</returns>
    Task<List<MonthlyStatistics>> GetYearlyStatisticsAsync(int userId, int year);
}

/// <summary>
/// 月度收支统计信息
/// </summary>
public class MonthlyStatistics
{
    /// <summary>
    /// 年月(YYYY-MM)
    /// </summary>
    public string YearMonth { get; set; } = string.Empty;

    /// <summary>
    /// 总收入
    /// </summary>
    public decimal TotalIncome { get; set; }

    /// <summary>
    /// 总支出
    /// </summary>
    public decimal TotalExpense { get; set; }

    /// <summary>
    /// 净收入（收入-支出）
    /// </summary>
    public decimal NetIncome => TotalIncome - TotalExpense;

    /// <summary>
    /// 交易笔数
    /// </summary>
    public int TransactionCount { get; set; }

    /// <summary>
    /// 收入笔数
    /// </summary>
    public int IncomeCount { get; set; }

    /// <summary>
    /// 支出笔数
    /// </summary>
    public int ExpenseCount { get; set; }
}
