using Ledger.Db.Models;
using Ledger.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Ledger.SimpleTest;

public class TestMonthlyData
{
    public static async Task Main(string[] args)
    {
        Console.WriteLine("=== 测试月度数据 ===");

        try
        {
            // 配置数据库连接
            var connectionString = "Host=aws-0-ap-southeast-1.pooler.supabase.com;Port=6543;Database=postgres;Username=postgres.fexuauldombukkgqxbre;Password=****************;SSL Mode=Require;Trust Server Certificate=true;Timeout=30;Command Timeout=30;Connection Idle Lifetime=300;Maximum Pool Size=20";

            var options = new DbContextOptionsBuilder<LedgerContext>()
                .UseNpgsql(connectionString)
                .Options;

            using var context = new LedgerContext(options);

            // 创建简单的日志记录器
            using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            var logger = loggerFactory.CreateLogger<TransactionService>();

            // 创建TransactionService
            var transactionService = new TransactionService(context, logger);

            Console.WriteLine("\n--- 查询数据库中的所有交易月份 ---");

            // 查询所有交易的年月
            var allTransactions = await context.Transactions
                .Where(t => t.UserId == 1)
                .Select(t => new { t.TransactionDate, t.Amount, t.Type, t.YearMonth })
                .ToListAsync();

            Console.WriteLine($"用户1总共有 {allTransactions.Count} 笔交易");

            var monthlyGroups = allTransactions
                .GroupBy(t => t.YearMonth)
                .OrderBy(g => g.Key)
                .ToList();

            Console.WriteLine($"数据分布在 {monthlyGroups.Count} 个月份：");

            foreach (var group in monthlyGroups)
            {
                var income = group.Where(t => t.Type == "income").Sum(t => t.Amount);
                var expense = group.Where(t => t.Type == "expense").Sum(t => t.Amount);
                Console.WriteLine($"  {group.Key}: {group.Count()}笔交易 (收入: ¥{income:F2}, 支出: ¥{expense:F2})");
            }

            Console.WriteLine("\n--- 测试不同月份的交易查询 ---");

            // 测试几个不同的月份
            string[] testMonths = { "2025-04", "2025-05", "2025-06" };

            foreach (var month in testMonths)
            {
                try
                {
                    Console.WriteLine($"\n查询 {month} 的交易:");
                    var transactions = await transactionService.GetMonthlyTransactionsAsync(1, month);
                    Console.WriteLine($"✅ {month}: 找到 {transactions.Count} 笔交易");

                    if (transactions.Count > 0)
                    {
                        var income = transactions.Where(t => t.Type == "income").Sum(t => t.Amount);
                        var expense = transactions.Where(t => t.Type == "expense").Sum(t => t.Amount);
                        Console.WriteLine($"   收入: ¥{income:F2}, 支出: ¥{expense:F2}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ {month}: 查询失败 - {ex.Message}");
                }
            }

        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 测试失败: {ex.Message}");
            Console.WriteLine($"详细错误: {ex}");
        }

        Console.WriteLine("\n测试完成，按任意键退出...");
        Console.ReadKey();
    }
}
