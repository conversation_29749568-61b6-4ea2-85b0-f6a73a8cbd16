using Ledger.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Ledger.Test;

/// <summary>
/// 数据迁移测试
/// </summary>
public class DataMigrationTest
{
    public static async Task RunMigrationAsync()
    {
        Console.WriteLine("=== Supabase 到 LocalDB 数据迁移 ===");
        Console.WriteLine("⚠️  警告：此操作将清空 LocalDB 中的现有数据并从 Supabase 重新导入");
        Console.Write("确认继续？(y/N): ");
        
        var confirmation = Console.ReadLine();
        if (confirmation?.ToLower() != "y")
        {
            Console.WriteLine("迁移已取消");
            return;
        }

        // 创建主机
        var host = CreateHostBuilder().Build();
        await host.StartAsync();

        try
        {
            var migrationService = host.Services.GetRequiredService<DataMigrationService>();
            
            Console.WriteLine("\n开始数据迁移...");
            var success = await migrationService.MigrateDataAsync();
            
            if (success)
            {
                Console.WriteLine("\n🎉 数据迁移成功完成！");
                Console.WriteLine("您现在可以使用本地 LocalDB 数据库了。");
            }
            else
            {
                Console.WriteLine("\n❌ 数据迁移失败，请检查日志信息。");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n❌ 迁移过程中发生错误: {ex.Message}");
            Console.WriteLine($"详细错误: {ex}");
        }
        finally
        {
            await host.StopAsync();
        }
    }

    /// <summary>
    /// 预览源数据库数据
    /// </summary>
    public static async Task PreviewSourceDataAsync()
    {
        Console.WriteLine("=== 预览 Supabase 数据库数据 ===");

        // 创建主机
        var host = CreateHostBuilder().Build();
        await host.StartAsync();

        try
        {
            var migrationService = host.Services.GetRequiredService<DataMigrationService>();
            await migrationService.PreviewSourceDataAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 预览失败: {ex.Message}");
        }
        finally
        {
            await host.StopAsync();
        }
    }

    /// <summary>
    /// 验证迁移结果
    /// </summary>
    public static async Task ValidateMigrationAsync()
    {
        Console.WriteLine("=== 验证迁移结果 ===");

        // 创建主机
        var host = CreateHostBuilder().Build();
        await host.StartAsync();

        try
        {
            var migrationService = host.Services.GetRequiredService<DataMigrationService>();
            await migrationService.ValidateMigrationResultAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 验证失败: {ex.Message}");
        }
        finally
        {
            await host.StopAsync();
        }
    }

    private static IHostBuilder CreateHostBuilder()
    {
        return Host.CreateDefaultBuilder()
            .ConfigureServices((context, services) =>
            {
                // 注册数据迁移服务
                services.AddScoped<DataMigrationService>();

                // 配置日志
                services.AddLogging(builder =>
                {
                    builder.AddConsole();
                    builder.SetMinimumLevel(LogLevel.Information);
                });
            });
    }
}
