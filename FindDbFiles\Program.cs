﻿using Microsoft.Data.SqlClient;

Console.WriteLine("=== 查找 LocalDB 数据库文件位置 ===");

var connectionString = "Server=(localdb)\\MSSQLLocalDB;Database=master;Trusted_Connection=true;";

try
{
    using var connection = new SqlConnection(connectionString);
    await connection.OpenAsync();
    Console.WriteLine("✅ 连接到 LocalDB 成功");

    // 查询所有数据库及其文件位置
    var command = connection.CreateCommand();
    command.CommandText = @"
        SELECT
            db.name AS DatabaseName,
            mf.name AS LogicalName,
            mf.physical_name AS PhysicalPath,
            mf.type_desc AS FileType,
            CAST(mf.size * 8.0 / 1024 AS DECIMAL(10,2)) AS SizeMB
        FROM sys.databases db
        INNER JOIN sys.master_files mf ON db.database_id = mf.database_id
        WHERE db.name IN ('LedgerDb', 'LedgerDbTest', 'LedgerDbSimpleTest')
        ORDER BY db.name, mf.type";

    using var reader = await command.ExecuteReaderAsync();

    Console.WriteLine("\n📁 数据库文件位置:");
    Console.WriteLine(new string('=', 80));

    while (await reader.ReadAsync())
    {
        var dbName = reader.GetString(0);
        var logicalName = reader.GetString(1);
        var physicalPath = reader.GetString(2);
        var fileType = reader.GetString(3);
        var sizeMB = reader.GetDecimal(4);

        Console.WriteLine($"数据库: {dbName}");
        Console.WriteLine($"  逻辑名: {logicalName}");
        Console.WriteLine($"  文件类型: {fileType}");
        Console.WriteLine($"  物理路径: {physicalPath}");
        Console.WriteLine($"  大小: {sizeMB} MB");
        Console.WriteLine();
    }

    reader.Close();

    // 查询 LocalDB 实例的默认数据目录
    command.CommandText = "SELECT SERVERPROPERTY('InstanceDefaultDataPath') AS DefaultDataPath";
    var defaultPath = await command.ExecuteScalarAsync();
    Console.WriteLine($"📂 LocalDB 默认数据目录: {defaultPath}");

    connection.Close();
}
catch (Exception ex)
{
    Console.WriteLine($"❌ 查询失败: {ex.Message}");
}
