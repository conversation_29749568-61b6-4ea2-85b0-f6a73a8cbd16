using Ledger.Db.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Ledger.Services;

/// <summary>
/// 数据迁移服务 - 从 Supabase PostgreSQL 迁移到 LocalDB
/// </summary>
public class DataMigrationService
{
    private readonly ILogger<DataMigrationService> _logger;

    public DataMigrationService(ILogger<DataMigrationService> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 执行完整的数据迁移
    /// </summary>
    public async Task<bool> MigrateDataAsync()
    {
        try
        {
            _logger.LogInformation("开始数据迁移：从 Supabase PostgreSQL 到 LocalDB");

            // 1. 连接到源数据库 (Supabase PostgreSQL)
            var sourceContext = CreateSourceContext();
            var targetContext = CreateTargetContext();

            // 2. 验证连接
            if (!await TestConnectionsAsync(sourceContext, targetContext))
            {
                return false;
            }

            // 3. 迁移用户数据
            await MigrateUsersAsync(sourceContext, targetContext);

            // 4. 迁移分类数据
            await MigrateCategoriesAsync(sourceContext, targetContext);

            // 5. 迁移交易数据
            await MigrateTransactionsAsync(sourceContext, targetContext);

            // 6. 验证数据完整性
            await ValidateDataIntegrityAsync(sourceContext, targetContext);

            _logger.LogInformation("数据迁移完成！");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "数据迁移失败");
            return false;
        }
    }

    /// <summary>
    /// 创建源数据库上下文 (Supabase PostgreSQL)
    /// </summary>
    private LedgerContext CreateSourceContext()
    {
        var connectionString = "Host=aws-0-ap-southeast-1.pooler.supabase.com;Port=6543;Database=postgres;Username=postgres.fexuauldombukkgqxbre;Password=****************;SSL Mode=Require;Trust Server Certificate=true;Timeout=60;Command Timeout=60;Connection Idle Lifetime=600;Maximum Pool Size=5";

        var options = new DbContextOptionsBuilder<LedgerContext>()
            .UseNpgsql(connectionString, npgsqlOptions =>
            {
                npgsqlOptions.CommandTimeout(60);
            })
            .Options;

        return new LedgerContext(options);
    }

    /// <summary>
    /// 创建目标数据库上下文 (LocalDB)
    /// </summary>
    private LedgerContext CreateTargetContext()
    {
        // 使用绝对路径确保正确性
        var dataPath = @"F:\dotnet_workspace\ledger\Data\LedgerDb.mdf";
        var connectionString = $"Server=(localdb)\\MSSQLLocalDB;Database=LedgerDb;AttachDbFilename={dataPath};Trusted_Connection=true;MultipleActiveResultSets=true";

        var options = new DbContextOptionsBuilder<LedgerContext>()
            .UseSqlServer(connectionString)
            .Options;

        return new LedgerContext(options);
    }

    /// <summary>
    /// 测试数据库连接
    /// </summary>
    private async Task<bool> TestConnectionsAsync(LedgerContext sourceContext, LedgerContext targetContext)
    {
        try
        {
            _logger.LogInformation("测试源数据库连接 (Supabase)...");
            await sourceContext.Database.CanConnectAsync();
            _logger.LogInformation("✅ 源数据库连接成功");

            _logger.LogInformation("测试目标数据库连接 (LocalDB)...");
            await targetContext.Database.CanConnectAsync();
            _logger.LogInformation("✅ 目标数据库连接成功");

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "数据库连接测试失败");
            return false;
        }
    }

    /// <summary>
    /// 迁移用户数据
    /// </summary>
    private async Task MigrateUsersAsync(LedgerContext sourceContext, LedgerContext targetContext)
    {
        _logger.LogInformation("开始迁移用户数据...");

        var sourceUsers = await sourceContext.Users.ToListAsync();
        _logger.LogInformation($"源数据库中找到 {sourceUsers.Count} 个用户");

        // 清空目标表
        var existingUsers = await targetContext.Users.ToListAsync();
        if (existingUsers.Any())
        {
            targetContext.Users.RemoveRange(existingUsers);
            await targetContext.SaveChangesAsync();
            _logger.LogInformation("已清空目标数据库中的用户数据");
        }

        // 启用 IDENTITY_INSERT 以保持原有 ID
        await targetContext.Database.ExecuteSqlRawAsync("SET IDENTITY_INSERT [user] ON");

        try
        {
            // 迁移数据
            foreach (var user in sourceUsers)
            {
                var newUser = new User
                {
                    Id = user.Id,
                    Username = user.Username,
                    Email = user.Email,
                    CreatedAt = user.CreatedAt,
                    UpdatedAt = user.UpdatedAt,
                    AlipayUserId = user.AlipayUserId,
                    AvatarUrl = user.AvatarUrl,
                    WechatOpenid = user.WechatOpenid,
                    Nickname = user.Nickname,
                    GoogleId = user.GoogleId,
                    WechatUnionid = user.WechatUnionid,
                    HashedPassword = user.HashedPassword,
                    Disabled = user.Disabled
                };

                targetContext.Users.Add(newUser);
            }

            await targetContext.SaveChangesAsync();
        }
        finally
        {
            // 关闭 IDENTITY_INSERT
            await targetContext.Database.ExecuteSqlRawAsync("SET IDENTITY_INSERT [user] OFF");
        }
        _logger.LogInformation($"✅ 成功迁移 {sourceUsers.Count} 个用户");
    }

    /// <summary>
    /// 迁移分类数据
    /// </summary>
    private async Task MigrateCategoriesAsync(LedgerContext sourceContext, LedgerContext targetContext)
    {
        _logger.LogInformation("开始迁移分类数据...");

        var sourceCategories = await sourceContext.Categories.ToListAsync();
        _logger.LogInformation($"源数据库中找到 {sourceCategories.Count} 个分类");

        // 清空目标表
        var existingCategories = await targetContext.Categories.ToListAsync();
        if (existingCategories.Any())
        {
            targetContext.Categories.RemoveRange(existingCategories);
            await targetContext.SaveChangesAsync();
            _logger.LogInformation("已清空目标数据库中的分类数据");
        }

        // 启用 IDENTITY_INSERT 以保持原有 ID
        await targetContext.Database.ExecuteSqlRawAsync("SET IDENTITY_INSERT [Categories] ON");

        try
        {
            // 迁移数据
            foreach (var category in sourceCategories)
            {
                var newCategory = new Category
                {
                    Id = category.Id,
                    Name = category.Name,
                    Type = category.Type,
                    Icon = category.Icon,
                    CreatedAt = category.CreatedAt,
                    UpdatedAt = category.UpdatedAt,
                    IsDefault = category.IsDefault,
                    SortOrder = category.SortOrder
                };

                targetContext.Categories.Add(newCategory);
            }

            await targetContext.SaveChangesAsync();
        }
        finally
        {
            // 关闭 IDENTITY_INSERT
            await targetContext.Database.ExecuteSqlRawAsync("SET IDENTITY_INSERT [Categories] OFF");
        }
        _logger.LogInformation($"✅ 成功迁移 {sourceCategories.Count} 个分类");
    }

    /// <summary>
    /// 迁移交易数据
    /// </summary>
    private async Task MigrateTransactionsAsync(LedgerContext sourceContext, LedgerContext targetContext)
    {
        _logger.LogInformation("开始迁移交易数据...");

        var sourceTransactions = await sourceContext.Transactions.ToListAsync();
        _logger.LogInformation($"源数据库中找到 {sourceTransactions.Count} 条交易记录");

        // 清空目标表
        var existingTransactions = await targetContext.Transactions.ToListAsync();
        if (existingTransactions.Any())
        {
            targetContext.Transactions.RemoveRange(existingTransactions);
            await targetContext.SaveChangesAsync();
            _logger.LogInformation("已清空目标数据库中的交易数据");
        }

        // 启用 IDENTITY_INSERT 以保持原有 ID
        await targetContext.Database.ExecuteSqlRawAsync("SET IDENTITY_INSERT [Transactions] ON");

        try
        {
            // 迁移数据
            foreach (var transaction in sourceTransactions)
            {
                var newTransaction = new Transaction
                {
                    Id = transaction.Id,
                    Amount = transaction.Amount,
                    Type = transaction.Type,
                    Description = transaction.Description,
                    TransactionDate = transaction.TransactionDate,
                    CreatedAt = transaction.CreatedAt,
                    UpdatedAt = transaction.UpdatedAt,
                    CategoryId = transaction.CategoryId,
                    UserId = transaction.UserId,
                    IsDeleted = transaction.IsDeleted,
                    YearMonth = transaction.YearMonth
                };

                targetContext.Transactions.Add(newTransaction);
            }

            await targetContext.SaveChangesAsync();
        }
        finally
        {
            // 关闭 IDENTITY_INSERT
            await targetContext.Database.ExecuteSqlRawAsync("SET IDENTITY_INSERT [Transactions] OFF");
        }
        _logger.LogInformation($"✅ 成功迁移 {sourceTransactions.Count} 条交易记录");
    }

    /// <summary>
    /// 验证数据完整性
    /// </summary>
    private async Task ValidateDataIntegrityAsync(LedgerContext sourceContext, LedgerContext targetContext)
    {
        _logger.LogInformation("开始验证数据完整性...");

        // 验证用户数量
        var sourceUserCount = await sourceContext.Users.CountAsync();
        var targetUserCount = await targetContext.Users.CountAsync();
        _logger.LogInformation($"用户数量 - 源: {sourceUserCount}, 目标: {targetUserCount}");

        // 验证分类数量
        var sourceCategoryCount = await sourceContext.Categories.CountAsync();
        var targetCategoryCount = await targetContext.Categories.CountAsync();
        _logger.LogInformation($"分类数量 - 源: {sourceCategoryCount}, 目标: {targetCategoryCount}");

        // 验证交易数量
        var sourceTransactionCount = await sourceContext.Transactions.CountAsync();
        var targetTransactionCount = await targetContext.Transactions.CountAsync();
        _logger.LogInformation($"交易数量 - 源: {sourceTransactionCount}, 目标: {targetTransactionCount}");

        // 验证数据一致性
        if (sourceUserCount == targetUserCount &&
            sourceCategoryCount == targetCategoryCount &&
            sourceTransactionCount == targetTransactionCount)
        {
            _logger.LogInformation("✅ 数据完整性验证通过");
        }
        else
        {
            _logger.LogWarning("⚠️ 数据完整性验证失败，数量不匹配");
        }
    }

    /// <summary>
    /// 预览源数据库数据
    /// </summary>
    public async Task PreviewSourceDataAsync()
    {
        try
        {
            _logger.LogInformation("正在连接到 Supabase 数据库...");
            using var sourceContext = CreateSourceContext();

            if (!await sourceContext.Database.CanConnectAsync())
            {
                _logger.LogError("无法连接到 Supabase 数据库");
                return;
            }

            _logger.LogInformation("✅ 成功连接到 Supabase 数据库");

            // 查询数据统计 - 使用更简单的查询
            var userCount = 0;
            var categoryCount = 0;
            var transactionCount = 0;

            try
            {
                userCount = await sourceContext.Users.CountAsync();
                _logger.LogInformation($"用户数量查询完成: {userCount}");
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"用户数量查询失败: {ex.Message}");
            }

            try
            {
                categoryCount = await sourceContext.Categories.CountAsync();
                _logger.LogInformation($"分类数量查询完成: {categoryCount}");
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"分类数量查询失败: {ex.Message}");
            }

            try
            {
                transactionCount = await sourceContext.Transactions.CountAsync();
                _logger.LogInformation($"交易数量查询完成: {transactionCount}");
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"交易数量查询失败: {ex.Message}");
            }

            Console.WriteLine($"\n📊 Supabase 数据库统计:");
            Console.WriteLine($"  用户数量: {userCount}");
            Console.WriteLine($"  分类数量: {categoryCount}");
            Console.WriteLine($"  交易记录数量: {transactionCount}");

            // 显示最近的一些数据
            if (userCount > 0)
            {
                Console.WriteLine($"\n👥 用户列表:");
                var users = await sourceContext.Users.Take(5).ToListAsync();
                foreach (var user in users)
                {
                    Console.WriteLine($"  ID: {user.Id}, 用户名: {user.Username}, 昵称: {user.Nickname}");
                }
                if (userCount > 5) Console.WriteLine($"  ... 还有 {userCount - 5} 个用户");
            }

            if (categoryCount > 0)
            {
                Console.WriteLine($"\n📂 分类列表:");
                var categories = await sourceContext.Categories.Take(10).ToListAsync();
                foreach (var category in categories)
                {
                    Console.WriteLine($"  ID: {category.Id}, 名称: {category.Name}, 类型: {category.Type}, 图标: {category.Icon}");
                }
                if (categoryCount > 10) Console.WriteLine($"  ... 还有 {categoryCount - 10} 个分类");
            }

            if (transactionCount > 0)
            {
                Console.WriteLine($"\n💰 最近交易记录:");
                var transactions = await sourceContext.Transactions
                    .OrderByDescending(t => t.CreatedAt)
                    .Take(5)
                    .ToListAsync();
                foreach (var transaction in transactions)
                {
                    Console.WriteLine($"  ID: {transaction.Id}, 金额: {transaction.Amount}, 类型: {transaction.Type}, 日期: {transaction.TransactionDate}");
                }
                if (transactionCount > 5) Console.WriteLine($"  ... 还有 {transactionCount - 5} 条记录");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "预览源数据失败");
        }
    }

    /// <summary>
    /// 验证迁移结果
    /// </summary>
    public async Task ValidateMigrationResultAsync()
    {
        try
        {
            _logger.LogInformation("正在验证迁移结果...");

            using var sourceContext = CreateSourceContext();
            using var targetContext = CreateTargetContext();

            // 测试连接
            var sourceConnected = await sourceContext.Database.CanConnectAsync();
            var targetConnected = await targetContext.Database.CanConnectAsync();

            Console.WriteLine($"\n🔗 数据库连接状态:");
            Console.WriteLine($"  Supabase: {(sourceConnected ? "✅ 连接成功" : "❌ 连接失败")}");
            Console.WriteLine($"  LocalDB: {(targetConnected ? "✅ 连接成功" : "❌ 连接失败")}");

            if (!sourceConnected || !targetConnected)
            {
                return;
            }

            // 比较数据数量
            var sourceUserCount = await sourceContext.Users.CountAsync();
            var targetUserCount = await targetContext.Users.CountAsync();
            var sourceCategoryCount = await sourceContext.Categories.CountAsync();
            var targetCategoryCount = await targetContext.Categories.CountAsync();
            var sourceTransactionCount = await sourceContext.Transactions.CountAsync();
            var targetTransactionCount = await targetContext.Transactions.CountAsync();

            Console.WriteLine($"\n📊 数据数量对比:");
            Console.WriteLine($"  用户: Supabase({sourceUserCount}) vs LocalDB({targetUserCount}) {(sourceUserCount == targetUserCount ? "✅" : "❌")}");
            Console.WriteLine($"  分类: Supabase({sourceCategoryCount}) vs LocalDB({targetCategoryCount}) {(sourceCategoryCount == targetCategoryCount ? "✅" : "❌")}");
            Console.WriteLine($"  交易: Supabase({sourceTransactionCount}) vs LocalDB({targetTransactionCount}) {(sourceTransactionCount == targetTransactionCount ? "✅" : "❌")}");

            // 验证数据完整性
            if (targetUserCount > 0)
            {
                var sampleUser = await targetContext.Users.FirstAsync();
                Console.WriteLine($"\n👤 LocalDB 用户样本: ID={sampleUser.Id}, 用户名={sampleUser.Username}");
            }

            if (targetCategoryCount > 0)
            {
                var sampleCategory = await targetContext.Categories.FirstAsync();
                Console.WriteLine($"📂 LocalDB 分类样本: ID={sampleCategory.Id}, 名称={sampleCategory.Name}");
            }

            if (targetTransactionCount > 0)
            {
                var sampleTransaction = await targetContext.Transactions.FirstAsync();
                Console.WriteLine($"💰 LocalDB 交易样本: ID={sampleTransaction.Id}, 金额={sampleTransaction.Amount}");
            }

            var allMatch = sourceUserCount == targetUserCount &&
                          sourceCategoryCount == targetCategoryCount &&
                          sourceTransactionCount == targetTransactionCount;

            Console.WriteLine($"\n🎯 迁移结果: {(allMatch ? "✅ 数据迁移完整" : "⚠️ 数据可能不完整")}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证迁移结果失败");
        }
    }
}
