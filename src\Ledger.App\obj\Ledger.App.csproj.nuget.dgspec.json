{"format": 1, "restore": {"F:\\dotnet_workspace\\ledger\\src\\Ledger.App\\Ledger.App.csproj": {}}, "projects": {"F:\\dotnet_workspace\\ledger\\src\\Ledger.App\\Ledger.App.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\dotnet_workspace\\ledger\\src\\Ledger.App\\Ledger.App.csproj", "projectName": "Ledger.App", "projectPath": "F:\\dotnet_workspace\\ledger\\src\\Ledger.App\\Ledger.App.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\dotnet_workspace\\ledger\\src\\Ledger.App\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"F:\\dotnet_workspace\\ledger\\src\\Ledger.Db\\Ledger.Db.csproj": {"projectPath": "F:\\dotnet_workspace\\ledger\\src\\Ledger.Db\\Ledger.Db.csproj"}, "F:\\dotnet_workspace\\ledger\\src\\Ledger.Services\\Ledger.Services.csproj": {"projectPath": "F:\\dotnet_workspace\\ledger\\src\\Ledger.Services\\Ledger.Services.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "F:\\dotnet_workspace\\ledger\\src\\Ledger.Db\\Ledger.Db.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\dotnet_workspace\\ledger\\src\\Ledger.Db\\Ledger.Db.csproj", "projectName": "Ledger.Db", "projectPath": "F:\\dotnet_workspace\\ledger\\src\\Ledger.Db\\Ledger.Db.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\dotnet_workspace\\ledger\\src\\Ledger.Db\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.1, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.1, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "F:\\dotnet_workspace\\ledger\\src\\Ledger.Services\\Ledger.Services.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\dotnet_workspace\\ledger\\src\\Ledger.Services\\Ledger.Services.csproj", "projectName": "Ledger.Services", "projectPath": "F:\\dotnet_workspace\\ledger\\src\\Ledger.Services\\Ledger.Services.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\dotnet_workspace\\ledger\\src\\Ledger.Services\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"F:\\dotnet_workspace\\ledger\\src\\Ledger.Db\\Ledger.Db.csproj": {"projectPath": "F:\\dotnet_workspace\\ledger\\src\\Ledger.Db\\Ledger.Db.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.1, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.5, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}