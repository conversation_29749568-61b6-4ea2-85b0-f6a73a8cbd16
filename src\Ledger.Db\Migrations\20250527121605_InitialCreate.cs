﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Ledger.Db.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "categories",
                columns: table => new
                {
                    id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    name = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, comment: "类别名称"),
                    type = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false, comment: "类型:income(收入)/expense(支出)"),
                    icon = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, defaultValue: "➕", comment: "类别图标"),
                    created_at = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETDATE()"),
                    updated_at = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETDATE()"),
                    is_default = table.Column<bool>(type: "bit", nullable: false, defaultValue: false, comment: "是否为默认分类"),
                    sort_order = table.Column<int>(type: "int", nullable: false, defaultValue: 0, comment: "排序顺序")
                },
                constraints: table =>
                {
                    table.PrimaryKey("categories_pkey", x => x.id);
                },
                comment: "收支类别表");

            migrationBuilder.CreateTable(
                name: "user",
                columns: table => new
                {
                    id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    username = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    email = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    created_at = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETDATE()"),
                    updated_at = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETDATE()"),
                    alipay_user_id = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    avatar_url = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    wechat_openid = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    nickname = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    google_id = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    wechat_unionid = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    hashed_password = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: true),
                    disabled = table.Column<bool>(type: "bit", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("user_pkey", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "transactions",
                columns: table => new
                {
                    id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    amount = table.Column<decimal>(type: "decimal(10,2)", precision: 10, scale: 2, nullable: false, comment: "金额"),
                    type = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false, comment: "类型:income(收入)/expense(支出)"),
                    description = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true, comment: "描述"),
                    transaction_date = table.Column<DateOnly>(type: "date", nullable: false, comment: "交易日期"),
                    created_at = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETDATE()"),
                    updated_at = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETDATE()"),
                    category_id = table.Column<int>(type: "int", nullable: false),
                    user_id = table.Column<int>(type: "int", nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false, comment: "软删除标记"),
                    year_month = table.Column<string>(type: "nvarchar(7)", maxLength: 7, nullable: false, comment: "年月(YYYY-MM)")
                },
                constraints: table =>
                {
                    table.PrimaryKey("transactions_pkey", x => x.id);
                    table.ForeignKey(
                        name: "transactions_category_id_fkey",
                        column: x => x.category_id,
                        principalTable: "categories",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "transactions_user_id_fkey",
                        column: x => x.user_id,
                        principalTable: "user",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                },
                comment: "收支流水记录表");

            migrationBuilder.CreateIndex(
                name: "IX_transactions_category_id",
                table: "transactions",
                column: "category_id");

            migrationBuilder.CreateIndex(
                name: "IX_transactions_user_id",
                table: "transactions",
                column: "user_id");

            migrationBuilder.CreateIndex(
                name: "uid_user_alipay__872cc6",
                table: "user",
                column: "alipay_user_id",
                unique: true,
                filter: "[alipay_user_id] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "uid_user_google__cf4521",
                table: "user",
                column: "google_id",
                unique: true,
                filter: "[google_id] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "uid_user_wechat__b23a2b",
                table: "user",
                column: "wechat_openid",
                unique: true,
                filter: "[wechat_openid] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "uid_user_wechat__b5f218",
                table: "user",
                column: "wechat_unionid",
                unique: true,
                filter: "[wechat_unionid] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "user_alipay_user_id_key",
                table: "user",
                column: "alipay_user_id",
                unique: true,
                filter: "[alipay_user_id] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "user_email_key",
                table: "user",
                column: "email",
                unique: true,
                filter: "[email] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "user_google_id_key",
                table: "user",
                column: "google_id",
                unique: true,
                filter: "[google_id] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "user_username_key",
                table: "user",
                column: "username",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "user_wechat_openid_key",
                table: "user",
                column: "wechat_openid",
                unique: true,
                filter: "[wechat_openid] IS NOT NULL");

            migrationBuilder.CreateIndex(
                name: "user_wechat_unionid_key",
                table: "user",
                column: "wechat_unionid",
                unique: true,
                filter: "[wechat_unionid] IS NOT NULL");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "transactions");

            migrationBuilder.DropTable(
                name: "categories");

            migrationBuilder.DropTable(
                name: "user");
        }
    }
}
