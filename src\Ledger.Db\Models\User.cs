﻿using System;
using System.Collections.Generic;

namespace Ledger.Db.Models;

/// <summary>
/// Auth: Stores user login data within a secure schema.
/// </summary>
public partial class User
{
    public int Id { get; set; }

    public string Username { get; set; } = null!;

    public string? Email { get; set; }

    public DateTime CreatedAt { get; set; }

    public DateTime UpdatedAt { get; set; }

    public string? AlipayUserId { get; set; }

    public string? AvatarUrl { get; set; }

    public string? WechatOpenid { get; set; }

    public string Nickname { get; set; } = null!;

    public string? GoogleId { get; set; }

    public string? WechatUnionid { get; set; }

    public string? HashedPassword { get; set; }

    public bool Disabled { get; set; }

    public virtual ICollection<Transaction> Transactions { get; set; } = new List<Transaction>();
}
