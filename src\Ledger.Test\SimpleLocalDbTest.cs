using Ledger.Db.Models;
using Microsoft.EntityFrameworkCore;

namespace Ledger.Test;

/// <summary>
/// 简单的 LocalDB 连接测试
/// </summary>
public class SimpleLocalDbTest
{
    public static async Task RunAsync()
    {
        Console.WriteLine("=== 简单 LocalDB 连接测试 ===");

        var connectionString = "Server=(localdb)\\MSSQLLocalDB;Database=LedgerDbSimpleTest;Trusted_Connection=true;MultipleActiveResultSets=true";
        
        var options = new DbContextOptionsBuilder<LedgerContext>()
            .UseSqlServer(connectionString)
            .Options;

        try
        {
            using var context = new LedgerContext(options);
            
            Console.WriteLine($"连接字符串: {connectionString}");
            
            // 测试连接
            Console.WriteLine("正在测试数据库连接...");
            var canConnect = await context.Database.CanConnectAsync();
            Console.WriteLine($"连接测试结果: {(canConnect ? "成功" : "失败")}");
            
            if (!canConnect)
            {
                Console.WriteLine("尝试创建数据库...");
                await context.Database.EnsureCreatedAsync();
                Console.WriteLine("数据库创建完成");
                
                // 再次测试连接
                canConnect = await context.Database.CanConnectAsync();
                Console.WriteLine($"创建后连接测试: {(canConnect ? "成功" : "失败")}");
            }
            
            if (canConnect)
            {
                // 测试基本操作
                Console.WriteLine("测试基本数据库操作...");
                
                // 检查表是否存在
                var tablesExist = await context.Database.CanConnectAsync();
                Console.WriteLine($"表结构检查: {(tablesExist ? "正常" : "异常")}");
                
                // 尝试查询数据
                var userCount = await context.Users.CountAsync();
                var categoryCount = await context.Categories.CountAsync();
                var transactionCount = await context.Transactions.CountAsync();
                
                Console.WriteLine($"用户数量: {userCount}");
                Console.WriteLine($"分类数量: {categoryCount}");
                Console.WriteLine($"交易数量: {transactionCount}");
                
                Console.WriteLine("✅ LocalDB 测试成功完成！");
            }
            else
            {
                Console.WriteLine("❌ LocalDB 连接失败");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 测试过程中发生错误: {ex.Message}");
            Console.WriteLine($"详细错误: {ex}");
        }
    }
}
