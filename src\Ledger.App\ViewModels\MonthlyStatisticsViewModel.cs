using Ledger.Services;
using Microsoft.Extensions.Logging;

namespace Ledger.App.ViewModels;

/// <summary>
/// 月度统计视图模型
/// </summary>
public class MonthlyStatisticsViewModel : ViewModelBase
{
    private readonly ITransactionService _transactionService;
    private readonly ILogger _logger;

    private MonthlyStatistics? _statistics;
    private bool _isLoading;

    public MonthlyStatisticsViewModel(ITransactionService transactionService, ILogger logger)
    {
        _transactionService = transactionService;
        _logger = logger;
    }

    #region 属性

    /// <summary>
    /// 统计数据
    /// </summary>
    public MonthlyStatistics? Statistics
    {
        get => _statistics;
        set => SetProperty(ref _statistics, value, OnStatisticsChanged);
    }

    /// <summary>
    /// 是否正在加载
    /// </summary>
    public bool IsLoading
    {
        get => _isLoading;
        set => SetProperty(ref _isLoading, value);
    }

    /// <summary>
    /// 年月
    /// </summary>
    public string YearMonth => _statistics?.YearMonth ?? string.Empty;

    /// <summary>
    /// 格式化的年月显示
    /// </summary>
    public string FormattedYearMonth
    {
        get
        {
            if (_statistics?.YearMonth != null &&
                DateTime.TryParseExact(_statistics.YearMonth, "yyyy-MM", null, System.Globalization.DateTimeStyles.None, out var date))
            {
                return date.ToString("yyyy年MM月");
            }
            return string.Empty;
        }
    }

    /// <summary>
    /// 总收入
    /// </summary>
    public decimal TotalIncome => _statistics?.TotalIncome ?? 0;

    /// <summary>
    /// 格式化的总收入
    /// </summary>
    public string FormattedTotalIncome => $"¥{TotalIncome:F2}";

    /// <summary>
    /// 总支出
    /// </summary>
    public decimal TotalExpense => _statistics?.TotalExpense ?? 0;

    /// <summary>
    /// 格式化的总支出
    /// </summary>
    public string FormattedTotalExpense => $"¥{TotalExpense:F2}";

    /// <summary>
    /// 净收入
    /// </summary>
    public decimal NetIncome => _statistics?.NetIncome ?? 0;

    /// <summary>
    /// 格式化的净收入
    /// </summary>
    public string FormattedNetIncome
    {
        get
        {
            var prefix = NetIncome >= 0 ? "+" : "";
            return $"{prefix}¥{NetIncome:F2}";
        }
    }

    /// <summary>
    /// 净收入颜色
    /// </summary>
    public string NetIncomeColor => NetIncome >= 0 ? "#4CAF50" : "#F44336";

    /// <summary>
    /// 交易笔数
    /// </summary>
    public int TransactionCount => _statistics?.TransactionCount ?? 0;

    /// <summary>
    /// 收入笔数
    /// </summary>
    public int IncomeCount => _statistics?.IncomeCount ?? 0;

    /// <summary>
    /// 支出笔数
    /// </summary>
    public int ExpenseCount => _statistics?.ExpenseCount ?? 0;

    /// <summary>
    /// 收入占比
    /// </summary>
    public double IncomePercentage
    {
        get
        {
            if (TransactionCount == 0) return 0;
            return (double)IncomeCount / TransactionCount * 100;
        }
    }

    /// <summary>
    /// 支出占比
    /// </summary>
    public double ExpensePercentage
    {
        get
        {
            if (TransactionCount == 0) return 0;
            return (double)ExpenseCount / TransactionCount * 100;
        }
    }

    /// <summary>
    /// 格式化的收入占比
    /// </summary>
    public string FormattedIncomePercentage => $"{IncomePercentage:F1}%";

    /// <summary>
    /// 格式化的支出占比
    /// </summary>
    public string FormattedExpensePercentage => $"{ExpensePercentage:F1}%";

    /// <summary>
    /// 平均每笔收入
    /// </summary>
    public decimal AverageIncome
    {
        get
        {
            if (IncomeCount == 0) return 0;
            return TotalIncome / IncomeCount;
        }
    }

    /// <summary>
    /// 格式化的平均每笔收入
    /// </summary>
    public string FormattedAverageIncome => $"¥{AverageIncome:F2}";

    /// <summary>
    /// 平均每笔支出
    /// </summary>
    public decimal AverageExpense
    {
        get
        {
            if (ExpenseCount == 0) return 0;
            return TotalExpense / ExpenseCount;
        }
    }

    /// <summary>
    /// 格式化的平均每笔支出
    /// </summary>
    public string FormattedAverageExpense => $"¥{AverageExpense:F2}";

    /// <summary>
    /// 是否有数据
    /// </summary>
    public bool HasData => _statistics != null && TransactionCount > 0;

    #endregion

    #region 方法

    /// <summary>
    /// 加载统计数据
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="yearMonth">年月</param>
    public async Task LoadStatisticsAsync(int userId, string yearMonth)
    {
        try
        {
            IsLoading = true;

            var statistics = await _transactionService.GetMonthlyStatisticsAsync(userId, yearMonth);
            Statistics = statistics;

            _logger.LogInformation($"Loaded statistics for user {userId} in {yearMonth}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error loading statistics for user {userId} in {yearMonth}");
            Statistics = null;
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 统计数据变更时的处理
    /// </summary>
    private void OnStatisticsChanged()
    {
        // 通知所有计算属性变更
        OnPropertyChanged(nameof(YearMonth));
        OnPropertyChanged(nameof(FormattedYearMonth));
        OnPropertyChanged(nameof(TotalIncome));
        OnPropertyChanged(nameof(FormattedTotalIncome));
        OnPropertyChanged(nameof(TotalExpense));
        OnPropertyChanged(nameof(FormattedTotalExpense));
        OnPropertyChanged(nameof(NetIncome));
        OnPropertyChanged(nameof(FormattedNetIncome));
        OnPropertyChanged(nameof(NetIncomeColor));
        OnPropertyChanged(nameof(TransactionCount));
        OnPropertyChanged(nameof(IncomeCount));
        OnPropertyChanged(nameof(ExpenseCount));
        OnPropertyChanged(nameof(IncomePercentage));
        OnPropertyChanged(nameof(ExpensePercentage));
        OnPropertyChanged(nameof(FormattedIncomePercentage));
        OnPropertyChanged(nameof(FormattedExpensePercentage));
        OnPropertyChanged(nameof(AverageIncome));
        OnPropertyChanged(nameof(FormattedAverageIncome));
        OnPropertyChanged(nameof(AverageExpense));
        OnPropertyChanged(nameof(FormattedAverageExpense));
        OnPropertyChanged(nameof(HasData));
    }

    #endregion
}
