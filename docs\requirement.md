# 项目名称：个人收支流水日志

## 项目简介：记录个人日常收入支出流水，实现收支统计分析

## 项目目标：

1. 实现收支流水记录
2. 实现月收支流水列表，月统计
3. 实现按年收支月流水统计列表

## 数据库及数据结构
1. 数据库：postgresql
2. 数据库平台：supabase
3. 表结构脚本：（注意：数据库表已存在，无需创建表）
    3.1. user表：
   create table public.user (
  id serial not null,
  username character varying(50) not null,
  email character varying(100) null,
  created_at timestamp with time zone not null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone not null default CURRENT_TIMESTAMP,
  alipay_user_id character varying(50) null,
  avatar_url character varying(200) null,
  wechat_openid character varying(50) null,
  nickname character varying(50) not null,
  google_id character varying(50) null,
  wechat_unionid character varying(50) null,
  hashed_password character varying(128) null,
  disabled boolean not null default false,
  constraint user_pkey primary key (id),
  constraint user_email_key unique (email),
  constraint user_google_id_key unique (google_id),
  constraint user_alipay_user_id_key unique (alipay_user_id),
  constraint user_username_key unique (username),
  constraint user_wechat_openid_key unique (wechat_openid),
  constraint user_wechat_unionid_key unique (wechat_unionid)
) TABLESPACE pg_default;
    3.2. categories表：
    create table public.categories (
  id serial not null,
  name character varying(50) not null,
  type character varying(10) not null,
  icon character varying(50) not null default '➕'::character varying,
  created_at timestamp with time zone not null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone not null default CURRENT_TIMESTAMP,
  is_default boolean not null default false,
  sort_order integer not null default 0,
  constraint categories_pkey primary key (id)
) TABLESPACE pg_default;
    3.3. transactions表：
    create table public.transactions (
  id serial not null,
  amount numeric(10, 2) not null,
  type character varying(10) not null,
  description character varying(200) null,
  transaction_date date not null,
  created_at timestamp with time zone not null default CURRENT_TIMESTAMP,
  updated_at timestamp with time zone not null default CURRENT_TIMESTAMP,
  category_id integer not null,
  user_id integer not null,
  is_deleted boolean not null default false,
  year_month character varying(7) not null,
  constraint transactions_pkey primary key (id),
  constraint transactions_category_id_fkey foreign KEY (category_id) references categories (id) on delete CASCADE,
  constraint transactions_user_id_fkey foreign KEY (user_id) references "user" (id) on delete CASCADE
) TABLESPACE pg_default;

## 开发语言
.net 8 WPF C#

## UI
1. 月流水列表（发生时间降序）
2. 添加流水
3. 月流水统计分析