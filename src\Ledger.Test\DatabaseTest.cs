using Ledger.Db.Models;
using Ledger.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Ledger.Test;

public class DatabaseTest
{
    public static async Task TestDatabaseConnection()
    {
        // 配置服务
        var services = new ServiceCollection();
        
        // 添加配置
        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json")
            .Build();
        
        // 添加日志
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(LogLevel.Information);
        });
        
        // 添加数据库上下文
        services.AddDbContext<LedgerContext>(options =>
            options.UseNpgsql(configuration.GetConnectionString("DefaultConnection")));
        
        // 添加服务
        services.AddScoped<DataInitializationService>();
        
        var serviceProvider = services.BuildServiceProvider();
        
        try
        {
            using var scope = serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<LedgerContext>();
            var dataInitService = scope.ServiceProvider.GetRequiredService<DataInitializationService>();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<DatabaseTest>>();
            
            logger.LogInformation("Testing database connection...");
            
            // 测试数据库连接
            var canConnect = await context.Database.CanConnectAsync();
            logger.LogInformation($"Database connection test: {(canConnect ? "SUCCESS" : "FAILED")}");
            
            if (canConnect)
            {
                // 初始化默认数据
                logger.LogInformation("Initializing default data...");
                await dataInitService.InitializeDefaultDataAsync();
                
                // 检查分类数据
                var categoryCount = await context.Categories.CountAsync();
                logger.LogInformation($"Categories in database: {categoryCount}");
                
                // 显示前几个分类
                var categories = await context.Categories.Take(5).ToListAsync();
                foreach (var category in categories)
                {
                    logger.LogInformation($"Category: {category.Name} ({category.Type}) - {category.Icon}");
                }
            }
        }
        catch (Exception ex)
        {
            var logger = serviceProvider.GetRequiredService<ILogger<DatabaseTest>>();
            logger.LogError(ex, "Database test failed");
        }
    }
}
