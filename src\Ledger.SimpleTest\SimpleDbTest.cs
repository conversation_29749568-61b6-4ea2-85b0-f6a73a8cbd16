using Ledger.Db.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace Ledger.SimpleTest;

public class SimpleDbTest
{
    public static async Task TestConnection()
    {
        Console.WriteLine("=== 简单数据库连接测试 ===");

        try
        {
            // 读取配置
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json")
                .Build();

            var connectionString = configuration.GetConnectionString("DefaultConnection");
            Console.WriteLine($"连接字符串: {connectionString?.Substring(0, Math.Min(50, connectionString.Length))}...");

            // 创建数据库上下文
            var options = new DbContextOptionsBuilder<LedgerContext>()
                .UseNpgsql(connectionString)
                .Options;

            using var context = new LedgerContext(options);

            // 测试连接
            Console.WriteLine("正在测试数据库连接...");
            var canConnect = await context.Database.CanConnectAsync();
            Console.WriteLine($"数据库连接测试: {(canConnect ? "✓ 成功" : "❌ 失败")}");

            if (canConnect)
            {
                // 确保数据库已创建
                Console.WriteLine("正在确保数据库已创建...");
                await context.Database.EnsureCreatedAsync();
                Console.WriteLine("✓ 数据库已创建");

                // 检查分类表
                Console.WriteLine("正在检查分类数据...");

                int categoryCount = 0;
                try
                {
                    // 使用超时控制
                    using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
                    categoryCount = await context.Categories.CountAsync(cts.Token);
                    Console.WriteLine($"✓ 分类数量: {categoryCount}");
                }
                catch (OperationCanceledException)
                {
                    Console.WriteLine("⚠️ 查询分类数据超时，尝试其他方法...");

                    // 尝试简单查询
                    try
                    {
                        var firstCategory = await context.Categories.FirstOrDefaultAsync();
                        if (firstCategory != null)
                        {
                            Console.WriteLine($"✓ 找到分类数据: {firstCategory.Name}");
                            categoryCount = 1; // 至少有一个
                        }
                        else
                        {
                            Console.WriteLine("✓ 分类表为空");
                            categoryCount = 0;
                        }
                    }
                    catch (Exception ex2)
                    {
                        Console.WriteLine($"⚠️ 查询分类失败: {ex2.Message}");
                        categoryCount = -1; // 表示查询失败
                    }
                }

                if (categoryCount == 0)
                {
                    Console.WriteLine("正在添加测试分类...");
                    var testCategory = new Category
                    {
                        Name = "测试分类",
                        Type = "expense",
                        Icon = "🧪",
                        IsDefault = false,
                        SortOrder = 999,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };

                    context.Categories.Add(testCategory);

                    try
                    {
                        using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
                        await context.SaveChangesAsync(cts.Token);
                        Console.WriteLine("✓ 测试分类已添加");
                    }
                    catch (OperationCanceledException)
                    {
                        Console.WriteLine("⚠️ 保存分类数据超时");
                    }
                }

                // 显示前几个分类
                if (categoryCount > 0)
                {
                    try
                    {
                        using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
                        var categories = await context.Categories.Take(5).ToListAsync(cts.Token);
                        Console.WriteLine("前5个分类:");
                        foreach (var category in categories)
                        {
                            Console.WriteLine($"  {category.Icon} {category.Name} ({category.Type})");
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        Console.WriteLine("⚠️ 查询分类列表超时");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 测试失败: {ex.Message}");
            Console.WriteLine($"详细错误: {ex}");
        }

        Console.WriteLine("\n测试完成，按任意键退出...");
        Console.ReadKey();
    }
}
