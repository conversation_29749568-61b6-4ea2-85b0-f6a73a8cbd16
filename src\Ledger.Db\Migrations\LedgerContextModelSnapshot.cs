﻿// <auto-generated />
using System;
using Ledger.Db.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Ledger.Db.Migrations
{
    [DbContext(typeof(LedgerContext))]
    partial class LedgerContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Ledger.Db.Models.Category", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<string>("Icon")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasDefaultValue("➕")
                        .HasColumnName("icon")
                        .HasComment("类别图标");

                    b.Property<bool>("IsDefault")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_default")
                        .HasComment("是否为默认分类");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("name")
                        .HasComment("类别名称");

                    b.Property<int>("SortOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0)
                        .HasColumnName("sort_order")
                        .HasComment("排序顺序");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("type")
                        .HasComment("类型:income(收入)/expense(支出)");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasColumnName("updated_at")
                        .HasDefaultValueSql("GETDATE()");

                    b.HasKey("Id")
                        .HasName("categories_pkey");

                    b.ToTable("categories", null, t =>
                        {
                            t.HasComment("收支类别表");
                        });
                });

            modelBuilder.Entity("Ledger.Db.Models.Transaction", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Amount")
                        .HasPrecision(10, 2)
                        .HasColumnType("decimal(10,2)")
                        .HasColumnName("amount")
                        .HasComment("金额");

                    b.Property<int>("CategoryId")
                        .HasColumnType("int")
                        .HasColumnName("category_id");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("description")
                        .HasComment("描述");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("is_deleted")
                        .HasComment("软删除标记");

                    b.Property<DateOnly>("TransactionDate")
                        .HasColumnType("date")
                        .HasColumnName("transaction_date")
                        .HasComment("交易日期");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("type")
                        .HasComment("类型:income(收入)/expense(支出)");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasColumnName("updated_at")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.Property<string>("YearMonth")
                        .IsRequired()
                        .HasMaxLength(7)
                        .HasColumnType("nvarchar(7)")
                        .HasColumnName("year_month")
                        .HasComment("年月(YYYY-MM)");

                    b.HasKey("Id")
                        .HasName("transactions_pkey");

                    b.HasIndex("CategoryId");

                    b.HasIndex("UserId");

                    b.ToTable("transactions", null, t =>
                        {
                            t.HasComment("收支流水记录表");
                        });
                });

            modelBuilder.Entity("Ledger.Db.Models.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AlipayUserId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("alipay_user_id");

                    b.Property<string>("AvatarUrl")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("avatar_url");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<bool>("Disabled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false)
                        .HasColumnName("disabled");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("email");

                    b.Property<string>("GoogleId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("google_id");

                    b.Property<string>("HashedPassword")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasColumnName("hashed_password");

                    b.Property<string>("Nickname")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("nickname");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasColumnName("updated_at")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("username");

                    b.Property<string>("WechatOpenid")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("wechat_openid");

                    b.Property<string>("WechatUnionid")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("wechat_unionid");

                    b.HasKey("Id")
                        .HasName("user_pkey");

                    b.HasIndex(new[] { "AlipayUserId" }, "uid_user_alipay__872cc6")
                        .IsUnique()
                        .HasFilter("[alipay_user_id] IS NOT NULL");

                    b.HasIndex(new[] { "GoogleId" }, "uid_user_google__cf4521")
                        .IsUnique()
                        .HasFilter("[google_id] IS NOT NULL");

                    b.HasIndex(new[] { "WechatOpenid" }, "uid_user_wechat__b23a2b")
                        .IsUnique()
                        .HasFilter("[wechat_openid] IS NOT NULL");

                    b.HasIndex(new[] { "WechatUnionid" }, "uid_user_wechat__b5f218")
                        .IsUnique()
                        .HasFilter("[wechat_unionid] IS NOT NULL");

                    b.HasIndex(new[] { "AlipayUserId" }, "user_alipay_user_id_key")
                        .IsUnique()
                        .HasFilter("[alipay_user_id] IS NOT NULL");

                    b.HasIndex(new[] { "Email" }, "user_email_key")
                        .IsUnique()
                        .HasFilter("[email] IS NOT NULL");

                    b.HasIndex(new[] { "GoogleId" }, "user_google_id_key")
                        .IsUnique()
                        .HasFilter("[google_id] IS NOT NULL");

                    b.HasIndex(new[] { "Username" }, "user_username_key")
                        .IsUnique();

                    b.HasIndex(new[] { "WechatOpenid" }, "user_wechat_openid_key")
                        .IsUnique()
                        .HasFilter("[wechat_openid] IS NOT NULL");

                    b.HasIndex(new[] { "WechatUnionid" }, "user_wechat_unionid_key")
                        .IsUnique()
                        .HasFilter("[wechat_unionid] IS NOT NULL");

                    b.ToTable("user", (string)null);
                });

            modelBuilder.Entity("Ledger.Db.Models.Transaction", b =>
                {
                    b.HasOne("Ledger.Db.Models.Category", "Category")
                        .WithMany("Transactions")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("transactions_category_id_fkey");

                    b.HasOne("Ledger.Db.Models.User", "User")
                        .WithMany("Transactions")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("transactions_user_id_fkey");

                    b.Navigation("Category");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Ledger.Db.Models.Category", b =>
                {
                    b.Navigation("Transactions");
                });

            modelBuilder.Entity("Ledger.Db.Models.User", b =>
                {
                    b.Navigation("Transactions");
                });
#pragma warning restore 612, 618
        }
    }
}
