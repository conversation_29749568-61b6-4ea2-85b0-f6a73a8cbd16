using Ledger.Db.Models;

namespace Ledger.App.ViewModels;

/// <summary>
/// 交易项视图模型
/// </summary>
public class TransactionItemViewModel : ViewModelBase
{
    private readonly Transaction _transaction;

    public TransactionItemViewModel(Transaction transaction)
    {
        _transaction = transaction ?? throw new ArgumentNullException(nameof(transaction));
    }

    /// <summary>
    /// 交易ID
    /// </summary>
    public int Id => _transaction.Id;

    /// <summary>
    /// 金额
    /// </summary>
    public decimal Amount => _transaction.Amount;

    /// <summary>
    /// 格式化的金额显示
    /// </summary>
    public string FormattedAmount
    {
        get
        {
            var prefix = _transaction.Type == "income" ? "+" : "-";
            return $"{prefix}¥{_transaction.Amount:F2}";
        }
    }

    /// <summary>
    /// 交易类型
    /// </summary>
    public string Type => _transaction.Type;

    /// <summary>
    /// 交易类型显示名称
    /// </summary>
    public string TypeDisplayName => _transaction.Type == "income" ? "收入" : "支出";

    /// <summary>
    /// 描述
    /// </summary>
    public string? Description => _transaction.Description;

    /// <summary>
    /// 交易日期
    /// </summary>
    public DateOnly TransactionDate => _transaction.TransactionDate;

    /// <summary>
    /// 格式化的交易日期
    /// </summary>
    public string FormattedTransactionDate => _transaction.TransactionDate.ToString("MM-dd");

    /// <summary>
    /// 完整的交易日期
    /// </summary>
    public string FullTransactionDate => _transaction.TransactionDate.ToString("yyyy-MM-dd");

    /// <summary>
    /// 分类ID
    /// </summary>
    public int CategoryId => _transaction.CategoryId;

    /// <summary>
    /// 分类名称
    /// </summary>
    public string CategoryName => _transaction.Category?.Name ?? "未知分类";

    /// <summary>
    /// 分类图标
    /// </summary>
    public string CategoryIcon => _transaction.Category?.Icon ?? "❓";

    /// <summary>
    /// 年月
    /// </summary>
    public string YearMonth => _transaction.YearMonth;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt => _transaction.CreatedAt;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt => _transaction.UpdatedAt;

    /// <summary>
    /// 是否为收入
    /// </summary>
    public bool IsIncome => _transaction.Type == "income";

    /// <summary>
    /// 是否为支出
    /// </summary>
    public bool IsExpense => _transaction.Type == "expense";

    /// <summary>
    /// 金额颜色（收入为绿色，支出为红色）
    /// </summary>
    public string AmountColor => IsIncome ? "#4CAF50" : "#F44336";

    /// <summary>
    /// 获取原始交易对象
    /// </summary>
    /// <returns>交易对象</returns>
    public Transaction GetTransaction() => _transaction;

    /// <summary>
    /// 更新交易数据
    /// </summary>
    /// <param name="transaction">新的交易数据</param>
    public void UpdateTransaction(Transaction transaction)
    {
        if (transaction.Id != _transaction.Id)
            throw new ArgumentException("Transaction ID mismatch");

        _transaction.Amount = transaction.Amount;
        _transaction.Type = transaction.Type;
        _transaction.Description = transaction.Description;
        _transaction.TransactionDate = transaction.TransactionDate;
        _transaction.CategoryId = transaction.CategoryId;
        _transaction.Category = transaction.Category;
        _transaction.YearMonth = transaction.YearMonth;
        _transaction.UpdatedAt = transaction.UpdatedAt;

        // 通知所有属性变更
        OnPropertyChanged(nameof(Amount));
        OnPropertyChanged(nameof(FormattedAmount));
        OnPropertyChanged(nameof(Type));
        OnPropertyChanged(nameof(TypeDisplayName));
        OnPropertyChanged(nameof(Description));
        OnPropertyChanged(nameof(TransactionDate));
        OnPropertyChanged(nameof(FormattedTransactionDate));
        OnPropertyChanged(nameof(FullTransactionDate));
        OnPropertyChanged(nameof(CategoryId));
        OnPropertyChanged(nameof(CategoryName));
        OnPropertyChanged(nameof(CategoryIcon));
        OnPropertyChanged(nameof(YearMonth));
        OnPropertyChanged(nameof(UpdatedAt));
        OnPropertyChanged(nameof(IsIncome));
        OnPropertyChanged(nameof(IsExpense));
        OnPropertyChanged(nameof(AmountColor));
    }
}
