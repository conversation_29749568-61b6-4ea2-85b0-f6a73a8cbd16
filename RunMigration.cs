using Ledger.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("=== 自动执行 Supabase 到 LocalDB 数据迁移 ===");

        // 创建主机
        var host = CreateHostBuilder().Build();
        await host.StartAsync();

        try
        {
            var migrationService = host.Services.GetRequiredService<DataMigrationService>();
            
            Console.WriteLine("开始数据迁移...");
            var success = await migrationService.MigrateDataAsync();
            
            if (success)
            {
                Console.WriteLine("\n🎉 数据迁移成功完成！");
                Console.WriteLine("您现在可以使用本地 LocalDB 数据库了。");
            }
            else
            {
                Console.WriteLine("\n❌ 数据迁移失败，请检查日志信息。");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n❌ 迁移过程中发生错误: {ex.Message}");
            Console.WriteLine($"详细错误: {ex}");
        }
        finally
        {
            await host.StopAsync();
        }
    }

    private static IHostBuilder CreateHostBuilder()
    {
        return Host.CreateDefaultBuilder()
            .ConfigureServices((context, services) =>
            {
                // 注册数据迁移服务
                services.AddScoped<DataMigrationService>();

                // 配置日志
                services.AddLogging(builder =>
                {
                    builder.AddConsole();
                    builder.SetMinimumLevel(LogLevel.Information);
                });
            });
    }
}
