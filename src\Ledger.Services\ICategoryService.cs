using Ledger.Db.Models;

namespace Ledger.Services;

/// <summary>
/// 分类服务接口
/// </summary>
public interface ICategoryService
{
    /// <summary>
    /// 获取所有分类列表（按排序顺序）
    /// </summary>
    /// <returns>分类列表</returns>
    Task<List<Category>> GetAllCategoriesAsync();

    /// <summary>
    /// 获取指定类型的分类列表
    /// </summary>
    /// <param name="type">类型：income(收入)/expense(支出)</param>
    /// <returns>分类列表</returns>
    Task<List<Category>> GetCategoriesByTypeAsync(string type);

    /// <summary>
    /// 根据ID获取分类
    /// </summary>
    /// <param name="categoryId">分类ID</param>
    /// <returns>分类对象</returns>
    Task<Category?> GetCategoryByIdAsync(int categoryId);

    /// <summary>
    /// 添加新分类
    /// </summary>
    /// <param name="category">分类对象</param>
    /// <returns>添加后的分类对象</returns>
    Task<Category> AddCategoryAsync(Category category);

    /// <summary>
    /// 更新分类
    /// </summary>
    /// <param name="category">分类对象</param>
    /// <returns>更新后的分类对象</returns>
    Task<Category> UpdateCategoryAsync(Category category);

    /// <summary>
    /// 删除分类
    /// </summary>
    /// <param name="categoryId">分类ID</param>
    /// <returns>是否删除成功</returns>
    Task<bool> DeleteCategoryAsync(int categoryId);
}
