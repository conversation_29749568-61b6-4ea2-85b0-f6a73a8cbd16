using Microsoft.Data.SqlClient;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("=== LocalDB 连接测试 ===");
        
        var connectionString = "Server=(localdb)\\MSSQLLocalDB;Database=TestDb;Trusted_Connection=true;";
        
        try
        {
            using var connection = new SqlConnection(connectionString);
            Console.WriteLine("正在尝试连接到 LocalDB...");
            await connection.OpenAsync();
            Console.WriteLine("✅ LocalDB 连接成功！");
            
            // 测试创建数据库
            var createDbCommand = connection.CreateCommand();
            createDbCommand.CommandText = "IF NOT EXISTS (SELECT * FROM sys.databases WHERE name = 'LedgerDbTest') CREATE DATABASE LedgerDbTest";
            await createDbCommand.ExecuteNonQueryAsync();
            Console.WriteLine("✅ 数据库创建成功！");
            
            connection.Close();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ LocalDB 连接失败: {ex.Message}");
            Console.WriteLine($"详细错误: {ex}");
        }
    }
}
