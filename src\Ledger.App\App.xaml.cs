﻿using System.Threading;
using System.Windows;
using Ledger.Services;
using Ledger.App.ViewModels;
using Ledger.Db.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Ledger.App
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        private IHost? _host;
        private static readonly Mutex _mutex = new Mutex(true, "PersonalLedgerApp");

        protected override async void OnStartup(StartupEventArgs e)
        {
            // 检查是否已有实例在运行
            if (!_mutex.WaitOne(TimeSpan.Zero, true))
            {
                MessageBox.Show("应用程序已在运行中！", "个人记账本", MessageBoxButton.OK, MessageBoxImage.Information);
                Shutdown();
                return;
            }

            // 创建主机
            _host = CreateHostBuilder().Build();

            // 启动主机
            await _host.StartAsync();

            // 不再初始化数据 - 数据已经存在
            // var dataInitService = _host.Services.GetRequiredService<DataInitializationService>();
            // await dataInitService.InitializeDefaultDataAsync();

            // 创建主窗口
            var mainWindow = _host.Services.GetRequiredService<MainWindow>();
            mainWindow.Show();

            base.OnStartup(e);
        }

        protected override async void OnExit(ExitEventArgs e)
        {
            if (_host != null)
            {
                await _host.StopAsync();
                _host.Dispose();
            }

            // 释放互斥锁
            _mutex.ReleaseMutex();
            _mutex.Dispose();

            base.OnExit(e);
        }

        private static IHostBuilder CreateHostBuilder()
        {
            return Host.CreateDefaultBuilder()
                .ConfigureAppConfiguration((context, config) =>
                {
                    config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
                })
                .ConfigureServices((context, services) =>
                {
                    // 配置数据库上下文工厂 - 避免并发问题
                    var connectionString = context.Configuration.GetConnectionString("DefaultConnection");
                    services.AddDbContextFactory<LedgerContext>(options =>
                        options.UseSqlServer(connectionString));

                    // 注册服务 - 使用 Transient 生命周期，每次都创建新实例
                    services.AddTransient<ITransactionService, TransactionService>();
                    services.AddTransient<ICategoryService, CategoryService>();
                    services.AddTransient<DataInitializationService>();

                    // 注册视图模型 - 使用 Transient，但依赖的服务是 Scoped
                    services.AddTransient<MainViewModel>();

                    // 注册窗口
                    services.AddTransient<MainWindow>();

                    // 配置日志
                    services.AddLogging(builder =>
                    {
                        builder.AddConsole();
                        builder.AddDebug();
                        builder.SetMinimumLevel(LogLevel.Information);
                    });
                });
        }
    }
}
