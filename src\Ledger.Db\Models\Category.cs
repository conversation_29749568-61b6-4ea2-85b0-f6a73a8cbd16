﻿using System;
using System.Collections.Generic;

namespace Ledger.Db.Models;

/// <summary>
/// 收支类别表
/// </summary>
public partial class Category
{
    public int Id { get; set; }

    /// <summary>
    /// 类别名称
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// 类型:income(收入)/expense(支出)
    /// </summary>
    public string Type { get; set; } = null!;

    /// <summary>
    /// 类别图标
    /// </summary>
    public string Icon { get; set; } = null!;

    public DateTime CreatedAt { get; set; }

    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// 是否为默认分类
    /// </summary>
    public bool IsDefault { get; set; }

    /// <summary>
    /// 排序顺序
    /// </summary>
    public int SortOrder { get; set; }

    public virtual ICollection<Transaction> Transactions { get; set; } = new List<Transaction>();
}
