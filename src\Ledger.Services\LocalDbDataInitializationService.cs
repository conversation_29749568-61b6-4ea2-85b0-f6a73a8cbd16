using Ledger.Db.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Ledger.Services;

/// <summary>
/// LocalDB 数据初始化服务
/// </summary>
public class LocalDbDataInitializationService
{
    private readonly IDbContextFactory<LedgerContext> _contextFactory;
    private readonly ILogger<LocalDbDataInitializationService> _logger;

    public LocalDbDataInitializationService(
        IDbContextFactory<LedgerContext> contextFactory,
        ILogger<LocalDbDataInitializationService> logger)
    {
        _contextFactory = contextFactory;
        _logger = logger;
    }

    /// <summary>
    /// 初始化 LocalDB 数据库和基础数据
    /// </summary>
    public async Task InitializeAsync()
    {
        try
        {
            using var context = _contextFactory.CreateDbContext();

            // 确保数据库已创建
            await context.Database.EnsureCreatedAsync();
            _logger.LogInformation("LocalDB 数据库已确保创建");

            // 初始化默认用户
            await InitializeDefaultUserAsync(context);

            // 初始化默认分类
            await InitializeDefaultCategoriesAsync(context);

            _logger.LogInformation("LocalDB 数据初始化完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "LocalDB 数据初始化失败");
            throw;
        }
    }

    /// <summary>
    /// 初始化默认用户
    /// </summary>
    private async Task InitializeDefaultUserAsync(LedgerContext context)
    {
        if (!await context.Users.AnyAsync())
        {
            var defaultUser = new User
            {
                Username = "default_user",
                Nickname = "默认用户",
                Email = "<EMAIL>",
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now,
                Disabled = false
            };

            context.Users.Add(defaultUser);
            await context.SaveChangesAsync();
            _logger.LogInformation("已创建默认用户");
        }
        else
        {
            _logger.LogInformation("默认用户已存在，跳过创建");
        }
    }

    /// <summary>
    /// 初始化默认分类
    /// </summary>
    private async Task InitializeDefaultCategoriesAsync(LedgerContext context)
    {
        if (!await context.Categories.AnyAsync())
        {
            var categories = new List<Category>
            {
                // 收入分类
                new Category { Name = "工资", Type = "income", Icon = "💰", IsDefault = true, SortOrder = 1 },
                new Category { Name = "奖金", Type = "income", Icon = "🎁", IsDefault = true, SortOrder = 2 },
                new Category { Name = "投资收益", Type = "income", Icon = "📈", IsDefault = true, SortOrder = 3 },
                new Category { Name = "其他收入", Type = "income", Icon = "💵", IsDefault = true, SortOrder = 4 },

                // 支出分类
                new Category { Name = "餐饮", Type = "expense", Icon = "🍽️", IsDefault = true, SortOrder = 1 },
                new Category { Name = "交通", Type = "expense", Icon = "🚗", IsDefault = true, SortOrder = 2 },
                new Category { Name = "购物", Type = "expense", Icon = "🛒", IsDefault = true, SortOrder = 3 },
                new Category { Name = "娱乐", Type = "expense", Icon = "🎮", IsDefault = true, SortOrder = 4 },
                new Category { Name = "医疗", Type = "expense", Icon = "🏥", IsDefault = true, SortOrder = 5 },
                new Category { Name = "教育", Type = "expense", Icon = "📚", IsDefault = true, SortOrder = 6 },
                new Category { Name = "住房", Type = "expense", Icon = "🏠", IsDefault = true, SortOrder = 7 },
                new Category { Name = "其他支出", Type = "expense", Icon = "💸", IsDefault = true, SortOrder = 8 }
            };

            foreach (var category in categories)
            {
                category.CreatedAt = DateTime.Now;
                category.UpdatedAt = DateTime.Now;
            }

            context.Categories.AddRange(categories);
            await context.SaveChangesAsync();
            _logger.LogInformation($"已创建 {categories.Count} 个默认分类");
        }
        else
        {
            _logger.LogInformation("默认分类已存在，跳过创建");
        }
    }

    /// <summary>
    /// 检查数据库连接
    /// </summary>
    public async Task<bool> TestConnectionAsync()
    {
        try
        {
            using var context = _contextFactory.CreateDbContext();
            _logger.LogInformation($"尝试连接到数据库: {context.Database.GetConnectionString()}");
            var canConnect = await context.Database.CanConnectAsync();
            _logger.LogInformation($"LocalDB 连接测试: {(canConnect ? "成功" : "失败")}");
            return canConnect;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "LocalDB 连接测试失败: {Message}", ex.Message);
            return false;
        }
    }
}
