﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>Ledger.App</id>
    <version>1.0.0</version>
    <authors>Ledger.App</authors>
    <description>Package Description</description>
    <repository type="git" />
    <dependencies>
      <group targetFramework="net8.0-windows7.0">
        <dependency id="Ledger.Db" version="1.0.0" exclude="Build,Analyzers" />
        <dependency id="Ledger.Services" version="1.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Configuration" version="9.0.5" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Configuration.Json" version="9.0.5" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.DependencyInjection" version="9.0.5" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Hosting" version="9.0.5" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Logging" version="9.0.5" exclude="Build,Analyzers" />
      </group>
    </dependencies>
    <frameworkReferences>
      <group targetFramework="net8.0-windows7.0">
        <frameworkReference name="Microsoft.WindowsDesktop.App.WPF" />
      </group>
    </frameworkReferences>
  </metadata>
  <files>
    <file src="F:\dotnet_workspace\ledger\src\Ledger.App\bin\Release\net8.0-windows\Ledger.App.runtimeconfig.json" target="lib\net8.0-windows7.0\Ledger.App.runtimeconfig.json" />
    <file src="F:\dotnet_workspace\ledger\src\Ledger.App\bin\Release\net8.0-windows\Ledger.App.dll" target="lib\net8.0-windows7.0\Ledger.App.dll" />
  </files>
</package>