using Ledger.Db.Models;
using Ledger.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Ledger.SimpleTest;

public class TestTransactionService
{
    public static async Task TestService()
    {
        Console.WriteLine("=== 测试 TransactionService ===");
        
        try
        {
            // 读取配置
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json")
                .Build();
            
            var connectionString = configuration.GetConnectionString("DefaultConnection");
            Console.WriteLine($"连接字符串: {connectionString}");
            
            // 创建数据库上下文
            var options = new DbContextOptionsBuilder<LedgerContext>()
                .UseNpgsql(connectionString)
                .Options;
            
            using var context = new LedgerContext(options);
            
            // 创建简单的日志记录器
            using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            var logger = loggerFactory.CreateLogger<TransactionService>();
            
            // 创建TransactionService
            var transactionService = new TransactionService(context, logger);
            
            Console.WriteLine("\n--- 测试 GetMonthlyTransactionsAsync ---");
            
            // 测试查询2025-05月的数据
            var userId = 1;
            var yearMonth = "2025-05";
            
            Console.WriteLine($"查询用户 {userId} 在 {yearMonth} 的交易记录...");
            
            var transactions = await transactionService.GetMonthlyTransactionsAsync(userId, yearMonth);
            
            Console.WriteLine($"✅ 查询成功！找到 {transactions.Count} 笔交易");
            
            foreach (var transaction in transactions.Take(5))
            {
                Console.WriteLine($"  - ID: {transaction.Id}, 日期: {transaction.TransactionDate}, " +
                                $"金额: {transaction.Amount}, 类型: {transaction.Type}, " +
                                $"描述: {transaction.Description}, 分类: {transaction.Category?.Name}");
            }
            
            if (transactions.Count > 5)
            {
                Console.WriteLine($"  ... 还有 {transactions.Count - 5} 笔交易");
            }
            
            Console.WriteLine("\n--- 测试 GetMonthlyStatisticsAsync ---");
            
            var statistics = await transactionService.GetMonthlyStatisticsAsync(userId, yearMonth);
            
            Console.WriteLine($"✅ 统计查询成功！");
            Console.WriteLine($"  - 总收入: {statistics.TotalIncome}");
            Console.WriteLine($"  - 总支出: {statistics.TotalExpense}");
            Console.WriteLine($"  - 净收入: {statistics.TotalIncome - statistics.TotalExpense}");
            Console.WriteLine($"  - 交易笔数: {statistics.TransactionCount}");
            Console.WriteLine($"  - 收入笔数: {statistics.IncomeCount}");
            Console.WriteLine($"  - 支出笔数: {statistics.ExpenseCount}");
            
            // 测试查询2024-12月的数据（应该没有数据）
            Console.WriteLine("\n--- 测试查询没有数据的月份 ---");
            var emptyYearMonth = "2024-12";
            Console.WriteLine($"查询用户 {userId} 在 {emptyYearMonth} 的交易记录...");
            
            var emptyTransactions = await transactionService.GetMonthlyTransactionsAsync(userId, emptyYearMonth);
            Console.WriteLine($"✅ 查询成功！找到 {emptyTransactions.Count} 笔交易（应该为0）");
            
            var emptyStatistics = await transactionService.GetMonthlyStatisticsAsync(userId, emptyYearMonth);
            Console.WriteLine($"✅ 统计查询成功！交易笔数: {emptyStatistics.TransactionCount}（应该为0）");
            
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 测试失败: {ex.Message}");
            Console.WriteLine($"详细错误: {ex}");
        }
        
        Console.WriteLine("\n测试完成，按任意键退出...");
        Console.ReadKey();
    }
}
