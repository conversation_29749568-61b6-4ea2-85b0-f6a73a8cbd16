using Ledger.Db.Models;
using Ledger.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Ledger.Test;

/// <summary>
/// LocalDB 迁移测试
/// </summary>
public class LocalDbMigrationTest
{
    public static async Task RunTestAsync()
    {
        Console.WriteLine("=== LocalDB 迁移测试开始 ===");

        // 创建主机
        var host = CreateHostBuilder().Build();
        await host.StartAsync();

        try
        {
            // 测试数据库连接
            await TestDatabaseConnectionAsync(host.Services);

            // 初始化数据
            await InitializeDataAsync(host.Services);

            // 测试基本功能
            await TestBasicFunctionalityAsync(host.Services);

            Console.WriteLine("\n✅ LocalDB 迁移测试成功完成！");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n❌ LocalDB 迁移测试失败: {ex.Message}");
            Console.WriteLine($"详细错误: {ex}");
        }
        finally
        {
            await host.StopAsync();
        }
    }

    private static async Task TestDatabaseConnectionAsync(IServiceProvider services)
    {
        Console.WriteLine("\n--- 测试数据库连接 ---");

        var initService = services.GetRequiredService<LocalDbDataInitializationService>();
        var canConnect = await initService.TestConnectionAsync();

        if (canConnect)
        {
            Console.WriteLine("✅ LocalDB 连接成功");
        }
        else
        {
            throw new Exception("LocalDB 连接失败");
        }
    }

    private static async Task InitializeDataAsync(IServiceProvider services)
    {
        Console.WriteLine("\n--- 初始化数据 ---");

        var initService = services.GetRequiredService<LocalDbDataInitializationService>();
        await initService.InitializeAsync();

        Console.WriteLine("✅ 数据初始化完成");
    }

    private static async Task TestBasicFunctionalityAsync(IServiceProvider services)
    {
        Console.WriteLine("\n--- 测试基本功能 ---");

        using var scope = services.CreateScope();
        var contextFactory = scope.ServiceProvider.GetRequiredService<IDbContextFactory<LedgerContext>>();

        using var context = contextFactory.CreateDbContext();

        // 测试用户数据
        var userCount = await context.Users.CountAsync();
        Console.WriteLine($"✅ 用户数量: {userCount}");

        // 测试分类数据
        var categoryCount = await context.Categories.CountAsync();
        Console.WriteLine($"✅ 分类数量: {categoryCount}");

        var incomeCategories = await context.Categories
            .Where(c => c.Type == "income")
            .CountAsync();
        Console.WriteLine($"✅ 收入分类数量: {incomeCategories}");

        var expenseCategories = await context.Categories
            .Where(c => c.Type == "expense")
            .CountAsync();
        Console.WriteLine($"✅ 支出分类数量: {expenseCategories}");

        // 测试创建交易记录
        var user = await context.Users.FirstAsync();
        var incomeCategory = await context.Categories
            .FirstAsync(c => c.Type == "income");

        var testTransaction = new Transaction
        {
            Amount = 1000.00m,
            Type = "income",
            Description = "测试收入",
            TransactionDate = DateOnly.FromDateTime(DateTime.Today),
            CategoryId = incomeCategory.Id,
            UserId = user.Id,
            YearMonth = DateTime.Today.ToString("yyyy-MM"),
            CreatedAt = DateTime.Now,
            UpdatedAt = DateTime.Now
        };

        context.Transactions.Add(testTransaction);
        await context.SaveChangesAsync();

        var transactionCount = await context.Transactions.CountAsync();
        Console.WriteLine($"✅ 交易记录数量: {transactionCount}");

        // 清理测试数据
        context.Transactions.Remove(testTransaction);
        await context.SaveChangesAsync();
        Console.WriteLine("✅ 测试数据已清理");
    }

    private static IHostBuilder CreateHostBuilder()
    {
        return Host.CreateDefaultBuilder()
            .ConfigureAppConfiguration((context, config) =>
            {
                // 获取当前程序集所在目录
                var basePath = Path.GetDirectoryName(typeof(LocalDbMigrationTest).Assembly.Location) ?? Directory.GetCurrentDirectory();
                config.SetBasePath(basePath);
                config.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
            })
            .ConfigureServices((context, services) =>
            {
                // 配置数据库上下文工厂
                var connectionString = context.Configuration.GetConnectionString("DefaultConnection");
                services.AddDbContextFactory<LedgerContext>(options =>
                    options.UseSqlServer(connectionString));

                // 注册服务
                services.AddScoped<LocalDbDataInitializationService>();
                services.AddScoped<ITransactionService, TransactionService>();
                services.AddScoped<ICategoryService, CategoryService>();

                // 配置日志
                services.AddLogging(builder =>
                {
                    builder.AddConsole();
                    builder.SetMinimumLevel(LogLevel.Information);
                });
            });
    }
}
